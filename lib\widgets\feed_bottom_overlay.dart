import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import '../screens/post_detail_screen.dart';

class FeedBottomOverlay extends StatelessWidget {
  final PostModel post;

  const FeedBottomOverlay({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 16,
      right: 16,
      bottom: 70, // Positioned above the custom bottom navigation bar
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left side - More options (3-dot menu)
          _buildActionButton(
            icon: Icons.more_vert,
            label: '',
            color: Colors.white,
            onPressed: () => _showPostOptions(context),
          ),

          // Right side - Comments, Likes, Reflex
          Row(
            children: [
              _buildActionButton(
                icon: Icons.chat_bubble_outline,
                label: post.commentCount.toString(),
                color: Colors.white,
                onPressed: () => _handleComment(context),
              ),
              const SizedBox(width: 24),
              _buildActionButton(
                icon:
                    post.isLikedByCurrentUser
                        ? Icons.shield
                        : Icons.shield_outlined,
                label: post.likeCount.toString(),
                color:
                    post.isLikedByCurrentUser
                        ? AppColors.gfGreen
                        : Colors.white,
                onPressed: () => _handleLike(context),
              ),
              const SizedBox(width: 24),
              _buildActionButton(
                icon: Icons.sports_martial_arts, // Sword-like icon for Reflex
                label: 'Reflex',
                color: Colors.white,
                onPressed: () => _handleReflex(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color.fromARGB(200, 96, 96, 96),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w800,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    await postsProvider.toggleLike(post.id);
  }

  void _handleComment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }

  void _handleReflex(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Reflex feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _handleShare(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.share, color: AppColors.gfOffWhite),
                  title: const Text(
                    'Share Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _handleShare(context);
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }
}
